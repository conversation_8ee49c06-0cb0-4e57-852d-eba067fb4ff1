"""
Pokemon Showdown Data Loader

Loads and parses data from Pokemon Showdown's data files.
"""

import json
import logging
import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import asyncio

from ..config import config
from ..models import Pokemon, Move, Item, Ability, PokemonStat, PokemonType

logger = logging.getLogger(__name__)


class ShowdownDataLoader:
    """Loads data from Pokemon Showdown data files"""
    
    def __init__(self):
        self.showdown_path = Path(config.showdown_path)
        self.data_path = self.showdown_path / "data"
        self.dist_path = self.showdown_path / "dist" / "data"
        self._cache: Dict[str, Any] = {}
    
    async def load_all_data(self) -> Dict[str, Any]:
        """Load all Pokemon data"""
        try:
            logger.info("Loading Pokemon data from Showdown...")
            
            data = {}
            
            # Load different data types
            data["pokemon"] = await self._load_pokemon_data()
            data["moves"] = await self._load_moves_data()
            data["items"] = await self._load_items_data()
            data["abilities"] = await self._load_abilities_data()
            data["natures"] = await self._load_natures_data()
            data["types"] = await self._load_types_data()
            
            logger.info(f"Loaded {len(data['pokemon'])} Pokemon, {len(data['moves'])} moves, "
                       f"{len(data['items'])} items, {len(data['abilities'])} abilities")
            
            return data
            
        except Exception as e:
            logger.error(f"Error loading Pokemon data: {e}")
            return {}
    
    async def _load_pokemon_data(self) -> Dict[str, Pokemon]:
        """Load Pokemon species data"""
        try:
            # Try to load from compiled JS first, then fall back to source
            pokedex_data = await self._load_js_data("pokedex.js")
            if not pokedex_data:
                logger.warning("Could not load compiled pokedex data, trying source files")
                return {}
            
            pokemon_dict = {}
            
            for pokemon_id, data in pokedex_data.items():
                try:
                    # Convert types
                    types = [PokemonType(t) for t in data.get("types", [])]
                    
                    # Convert base stats
                    base_stats_data = data.get("baseStats", {})
                    base_stats = PokemonStat(
                        hp=base_stats_data.get("hp", 0),
                        attack=base_stats_data.get("atk", 0),
                        defense=base_stats_data.get("def", 0),
                        special_attack=base_stats_data.get("spa", 0),
                        special_defense=base_stats_data.get("spd", 0),
                        speed=base_stats_data.get("spe", 0)
                    )
                    
                    # Convert abilities
                    abilities_data = data.get("abilities", {})
                    abilities = {}
                    for slot, ability in abilities_data.items():
                        abilities[str(slot)] = ability
                    
                    pokemon = Pokemon(
                        id=pokemon_id,
                        name=data.get("name", pokemon_id),
                        num=data.get("num", 0),
                        types=types,
                        base_stats=base_stats,
                        abilities=abilities,
                        height=data.get("heightm", 0.0),
                        weight=data.get("weightkg", 0.0),
                        color=data.get("color", ""),
                        egg_groups=data.get("eggGroups", []),
                        tier=data.get("tier"),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    pokemon_dict[pokemon_id] = pokemon
                    
                except Exception as e:
                    logger.warning(f"Error parsing Pokemon {pokemon_id}: {e}")
                    continue
            
            return pokemon_dict
            
        except Exception as e:
            logger.error(f"Error loading Pokemon data: {e}")
            return {}
    
    async def _load_moves_data(self) -> Dict[str, Move]:
        """Load moves data"""
        try:
            moves_data = await self._load_js_data("moves.js")
            if not moves_data:
                return {}
            
            moves_dict = {}
            
            for move_id, data in moves_data.items():
                try:
                    move = Move(
                        id=move_id,
                        name=data.get("name", move_id),
                        num=data.get("num", 0),
                        type=PokemonType(data.get("type", "Normal")),
                        category=data.get("category", "Status"),
                        base_power=data.get("basePower", 0),
                        accuracy=data.get("accuracy", True),
                        pp=data.get("pp", 0),
                        priority=data.get("priority", 0),
                        target=data.get("target", ""),
                        description=data.get("desc", ""),
                        short_description=data.get("shortDesc", ""),
                        flags=data.get("flags", {}),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    moves_dict[move_id] = move
                    
                except Exception as e:
                    logger.warning(f"Error parsing move {move_id}: {e}")
                    continue
            
            return moves_dict
            
        except Exception as e:
            logger.error(f"Error loading moves data: {e}")
            return {}
    
    async def _load_items_data(self) -> Dict[str, Item]:
        """Load items data"""
        try:
            items_data = await self._load_js_data("items.js")
            if not items_data:
                return {}
            
            items_dict = {}
            
            for item_id, data in items_data.items():
                try:
                    item = Item(
                        id=item_id,
                        name=data.get("name", item_id),
                        num=data.get("num", 0),
                        description=data.get("desc", ""),
                        short_description=data.get("shortDesc", ""),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    items_dict[item_id] = item
                    
                except Exception as e:
                    logger.warning(f"Error parsing item {item_id}: {e}")
                    continue
            
            return items_dict
            
        except Exception as e:
            logger.error(f"Error loading items data: {e}")
            return {}
    
    async def _load_abilities_data(self) -> Dict[str, Ability]:
        """Load abilities data"""
        try:
            abilities_data = await self._load_js_data("abilities.js")
            if not abilities_data:
                return {}
            
            abilities_dict = {}
            
            for ability_id, data in abilities_data.items():
                try:
                    ability = Ability(
                        id=ability_id,
                        name=data.get("name", ability_id),
                        num=data.get("num", 0),
                        description=data.get("desc", ""),
                        short_description=data.get("shortDesc", ""),
                        is_nonstandard=data.get("isNonstandard")
                    )
                    
                    abilities_dict[ability_id] = ability
                    
                except Exception as e:
                    logger.warning(f"Error parsing ability {ability_id}: {e}")
                    continue
            
            return abilities_dict
            
        except Exception as e:
            logger.error(f"Error loading abilities data: {e}")
            return {}
    
    async def _load_natures_data(self) -> Dict[str, Dict[str, Any]]:
        """Load natures data"""
        try:
            natures_data = await self._load_js_data("natures.js")
            return natures_data or {}
            
        except Exception as e:
            logger.error(f"Error loading natures data: {e}")
            return {}
    
    async def _load_types_data(self) -> Dict[str, Dict[str, Any]]:
        """Load type chart data"""
        try:
            types_data = await self._load_js_data("typechart.js")
            return types_data or {}
            
        except Exception as e:
            logger.error(f"Error loading types data: {e}")
            return {}
    
    async def _load_js_data(self, filename: str) -> Optional[Dict[str, Any]]:
        """Load data from a compiled JavaScript file"""
        try:
            file_path = self.dist_path / filename
            
            if not file_path.exists():
                logger.warning(f"Data file not found: {file_path}")
                return None
            
            # Read the JavaScript file
            content = file_path.read_text(encoding='utf-8')
            
            # Extract the data object from the JavaScript
            # The files typically export data like: exports.BattlePokedex = { ... }
            data = await self._parse_js_export(content)
            
            return data
            
        except Exception as e:
            logger.error(f"Error loading JS data from {filename}: {e}")
            return None
    
    async def _parse_js_export(self, content: str) -> Optional[Dict[str, Any]]:
        """Parse JavaScript export to extract data"""
        try:
            # Find the main data object
            # Look for patterns like const Pokedex = { ... }
            patterns = [
                r'const\s+(\w+)\s*=\s*(\{.*?\});',  # const Pokedex = { ... };
                r'exports\.(\w+)\s*=\s*(\{.*?\});',  # exports.Pokedex = { ... };
                r'module\.exports\s*=\s*(\{.*?\});',  # module.exports = { ... };
            ]

            match = None
            obj_str = None

            for pattern in patterns:
                match = re.search(pattern, content, re.DOTALL)
                if match:
                    if len(match.groups()) == 2:
                        obj_str = match.group(2)  # Get the object part
                    else:
                        obj_str = match.group(1)  # For module.exports pattern
                    break

            if not match or not obj_str:
                logger.warning("Could not find data object pattern in JS file")
                return None

            # Convert JavaScript object to JSON
            # This is a simplified conversion - may need more sophisticated parsing
            json_str = await self._js_to_json(obj_str)

            if json_str:
                return json.loads(json_str)

            return None

        except Exception as e:
            logger.error(f"Error parsing JS export: {e}")
            return None
    
    async def _js_to_json(self, js_obj: str) -> Optional[str]:
        """Convert JavaScript object notation to JSON"""
        try:
            # This is a simplified conversion
            # In a production environment, you might want to use a proper JS parser

            # Remove comments
            json_str = re.sub(r'//.*?$', '', js_obj, flags=re.MULTILINE)
            json_str = re.sub(r'/\*.*?\*/', '', json_str, flags=re.DOTALL)

            # Replace JavaScript-style property names with JSON-style
            # Handle both quoted and unquoted property names
            json_str = re.sub(r'(\w+):', r'"\1":', json_str)

            # Replace single quotes with double quotes
            json_str = re.sub(r"'([^']*)'", r'"\1"', json_str)

            # Handle undefined values
            json_str = re.sub(r'\bundefined\b', 'null', json_str)

            # Handle true/false/null (ensure they're lowercase)
            json_str = re.sub(r'\btrue\b', 'true', json_str)
            json_str = re.sub(r'\bfalse\b', 'false', json_str)
            json_str = re.sub(r'\bnull\b', 'null', json_str)

            # Remove trailing commas before closing brackets/braces
            json_str = re.sub(r',(\s*[}\]])', r'\1', json_str)

            return json_str

        except Exception as e:
            logger.error(f"Error converting JS to JSON: {e}")
            return None
